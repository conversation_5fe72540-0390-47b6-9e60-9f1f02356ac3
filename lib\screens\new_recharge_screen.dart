import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/providers/transaction_provider.dart';
import 'package:wardlytec_app/services/ad_manager.dart';
import 'package:wardlytec_app/services/rewards_notification_service.dart';
import 'package:wardlytec_app/utils/ad_connection_monitor.dart';
import 'package:wardlytec_app/widgets/connectivity_wrapper.dart';
import 'package:wardlytec_app/widgets/single_tap_button.dart';
import 'package:wardlytec_app/utils/app_theme.dart';
import 'package:wardlytec_app/utils/network_helper.dart';
import 'package:wardlytec_app/services/connectivity_service.dart';
import 'package:wardlytec_app/screens/transaction_history_screen.dart';

// إضافة typedef للوصول إلى State الصحيح
typedef SingleTapButtonState = State<SingleTapButton>;


class NewRechargeScreen extends StatefulWidget {
  final String selectedNetwork;

  const NewRechargeScreen({
    Key? key,
    required this.selectedNetwork,
  }) : super(key: key);

  @override
  State<NewRechargeScreen> createState() => _NewRechargeScreenState();
}

class _NewRechargeScreenState extends State<NewRechargeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _senderWalletController = TextEditingController();
  final _instapayNameController = TextEditingController();

  String? _selectedWallet;
  String? _selectedRechargeType;
  double? _selectedAmount;
  double _commission = 1.0; // عمولة ثابتة 1 جنيه
  double _totalAmount = 0;
  bool _hasWatchedAd = false;
  bool _hasWatchedAdInSession = false;
  static String? _sessionId;
  final GlobalKey<State<SingleTapButton>> _adButtonKey = GlobalKey<State<SingleTapButton>>(); // مفتاح زر الإعلان
  bool _isWalletSelectorExpanded = false;
  bool _isRechargeTypeSelectorExpanded = false;

  // 📸 متغيرات الصورة
  File? _proofImage;
  String? _proofImageBase64;
  final ImagePicker _imagePicker = ImagePicker();

  // 🛡️ حماية من الضغط المتكرر
  bool _isSubmitting = false;
  Timer? _debounceTimer;

  // قيم الشحن المتاحة (تتغير حسب النوع)
  List<double> _rechargeAmounts = [13, 16.5, 19.5, 26, 38, 45, 55, 65];

  // أنواع الشحن (تتغير حسب الشبكة)
  List<String> _rechargeTypes = [
    'شحن وحدات كارت الكبير',
    'كارت الكبير ميجابيتس',
  ];

  // قيم الشحن لكل نوع
  final Map<String, List<double>> _rechargeAmountsByType = {
    'شحن وحدات كارت الكبير': [13, 16.5, 19.5, 26, 38, 45, 55, 65],
    'كارت الكبير ميجابيتس': [13, 16.5, 19.5, 26, 38, 45, 55, 65],
    'شحن فودافون فكه': [13, 16.5, 19.5, 26],
    'اقوى كارت سوشيال': [13, 16.5, 19.5, 26],
    'اقوى كارت مكس': [13, 16.5, 19.5, 26, 38, 39, 65],
    'دقائق لكل الشبكات': [13, 16.5, 19.5, 26, 38, 39, 65],
  };

  // أرقام المحافظ الرقمية
  final Map<String, String> _walletNumbers = {
    'Vodafone Cash': '01091020551',
    'Etisalat Cash': '01108374378',
    'Orange Cash': '01201937252',
    'CIB Smart Wallet': '01212867407',
    'Instapay': 'wardlytec@instapay',
  };

  // المحافظ الرقمية (بدون ATM)
  final List<Map<String, dynamic>> _wallets = [
    {
      'name': 'Vodafone Cash',
      'displayName': 'فودافون كاش',
      'icon': Icons.phone_android,
      'color': Colors.red,
    },
    {
      'name': 'Orange Cash',
      'displayName': 'أورنج كاش',
      'icon': Icons.phone_iphone,
      'color': Colors.orange,
    },
    {
      'name': 'Etisalat Cash',
      'displayName': 'اتصالات كاش',
      'icon': Icons.smartphone,
      'color': Colors.green,
    },
    {
      'name': 'CIB Smart Wallet',
      'displayName': 'CIB Smart Wallet',
      'icon': Icons.account_balance,
      'color': Colors.blue,
    },
    {
      'name': 'Instapay',
      'displayName': 'إنستاباي',
      'icon': Icons.payment,
      'color': Colors.blue,
    },
  ];

  @override
  void initState() {
    super.initState();
    _generateSessionId();
    _phoneController.addListener(_updateFormState);
    _senderWalletController.addListener(_updateFormState);
    _instapayNameController.addListener(_updateFormState);

    // تحديث أنواع الشحن حسب الشبكة المختارة
    _updateRechargeTypesForNetwork();

    // تحميل حالة مشاهدة الإعلان في الجلسة
    _loadAdWatchedState();

    // تحميل عدد مرات الاستخدام
    _loadAdUsageCount();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _senderWalletController.dispose();
    _instapayNameController.dispose();
    _debounceTimer?.cancel();

    // تنظيف مراقب الاتصال
    AdConnectionMonitor.dispose();

    super.dispose();
  }

  void _generateSessionId() {
    _sessionId = DateTime.now().millisecondsSinceEpoch.toString();
  }

  // تحديث أنواع الشحن حسب الشبكة المختارة
  void _updateRechargeTypesForNetwork() {
    setState(() {
      final networkLower = widget.selectedNetwork.toLowerCase();

      if (networkLower == 'vodafone' || networkLower == 'فودافون') {
        _rechargeTypes = [
          'شحن فودافون فكه',
        ];
      } else if (networkLower == 'etisalat' || networkLower == 'اتصالات' || networkLower == 'إتصالات') {
        _rechargeTypes = [
          'اقوى كارت سوشيال',
          'اقوى كارت مكس',
          'دقائق لكل الشبكات',
        ];
      } else {
        // أورانج ووي
        _rechargeTypes = [
          'شحن وحدات كارت الكبير',
          'كارت الكبير ميجابيتس',
        ];
      }

      // إعادة تعيين النوع المختار إذا لم يعد متاحاً
      if (_selectedRechargeType != null && !_rechargeTypes.contains(_selectedRechargeType)) {
        _selectedRechargeType = null;
        _selectedAmount = null;
      }
    });
  }

  // تحديث قيم الشحن حسب النوع المختار
  void _updateRechargeAmountsForType(String rechargeType) {
    setState(() {
      _rechargeAmounts = _rechargeAmountsByType[rechargeType] ??
                       _rechargeAmountsByType['شحن وحدات كارت الكبير']!;

      // إعادة تعيين المبلغ المختار إذا لم يعد متاحاً
      if (_selectedAmount != null && !_rechargeAmounts.contains(_selectedAmount)) {
        _selectedAmount = null;
        _updateTotalAmount();
      }
    });
  }

  // فتح تطبيق إنستاباي
  Future<void> _openInstapay() async {
    try {
      // فتح رابط Instapay المخصص
      final Uri instapayUri = Uri.parse('https://ipn.eg/S/wardly/instapay/0FJD4E');

      if (await canLaunchUrl(instapayUri)) {
        await launchUrl(instapayUri, mode: LaunchMode.externalApplication);

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم فتح رابط Instapay'),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 3),
          ),
        );
        return;
      }

      // إذا فشل فتح الرابط، عرض رسالة خطأ
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تعذر فتح رابط Instapay'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فتح Instapay: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // تحميل عدد مرات استخدام الإعلان من قاعدة البيانات
  Future<void> _loadAdUsageCount() async {
    // في التطبيق الحقيقي، يجب تحميل هذا من Supabase أو SharedPreferences
    // هنا نقوم بمحاكاة تحميل البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    // لا حاجة لتعيين عدد المشاهدات - سيتم أخذه من المستخدم
  }

  // تنظيف الجلسات القديمة (أكثر من 24 ساعة)
  Future<void> _cleanupOldSessions() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys();
    final now = DateTime.now().millisecondsSinceEpoch;

    for (final key in keys) {
      if (key.startsWith('ad_watched_session_')) {
        try {
          final sessionTime = int.parse(key.split('_').last);
          if (now - sessionTime > 24 * 60 * 60 * 1000) { // 24 ساعة
            await prefs.remove(key);
          }
        } catch (e) {
          // في حالة خطأ في التحليل، احذف المفتاح
          await prefs.remove(key);
        }
      }
    }
  }

  // تحميل حالة مشاهدة الإعلان في الجلسة الحالية
  Future<void> _loadAdWatchedState() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionKey = 'ad_watched_${_getSessionId()}';
    final watchedInSession = prefs.getBool(sessionKey) ?? false;

    if (mounted) {
      setState(() {
        _hasWatchedAdInSession = watchedInSession;
      });
    }
  }

  void _updateTotalAmount() {
    if (_selectedAmount != null) {
      double effectiveCommission = _hasWatchedAd ? 0 : _commission;
      _totalAmount = _selectedAmount! + effectiveCommission;
    }
  }

  // اختيار مبلغ الشحن
  void _selectAmount(double amount) {
    setState(() {
      _selectedAmount = amount;
      _updateTotalAmount();
    });
    _updateFormState();
  }



  // الحصول على اسم نوع الشحن المختار
  String _getSelectedRechargeTypeDisplayName() {
    if (_selectedRechargeType == null) return 'اختر نوع الشحن';
    return _selectedRechargeType!;
  }

  // الحصول على اسم المحفظة المختارة
  String _getSelectedWalletDisplayName() {
    if (_selectedWallet == null) return 'اختر المحفظة الإلكترونية';
    try {
      final wallet = _wallets.firstWhere((w) => w['name'] == _selectedWallet);
      return wallet['displayName'];
    } catch (e) {
      // إذا لم يتم العثور على المحفظة في القائمة، إرجاع الاسم مباشرة
      return _selectedWallet == 'Instapay' ? 'إنستاباي' : _selectedWallet ?? 'غير محدد';
    }
  }

  // بناء واجهة اختيار نوع الشحن
  Widget _buildRechargeTypeSelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade600
              : Colors.grey.shade300,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // الزر الرئيسي لاختيار نوع الشحن
          InkWell(
            onTap: () {
              setState(() {
                _isRechargeTypeSelectorExpanded = !_isRechargeTypeSelectorExpanded;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // أيقونة السهم
                  Icon(
                    _isRechargeTypeSelectorExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                  ),
                  const Spacer(),
                  // النص والأيقونة
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          _getSelectedRechargeTypeDisplayName(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: _selectedRechargeType == null
                                ? (Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey.shade400
                                    : Colors.grey.shade600)
                                : (Theme.of(context).brightness == Brightness.dark
                                    ? Theme.of(context).textTheme.bodyLarge?.color
                                    : Colors.black87),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.category,
                          color: _selectedRechargeType == null
                              ? (Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey.shade600
                                  : Colors.grey.shade400)
                              : AppTheme.primaryColor,
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // خيارات أنواع الشحن (قابلة للتوسيع)
          if (_isRechargeTypeSelectorExpanded) ...[
            const Divider(height: 1),
            ...(_rechargeTypes.map((type) => _buildRechargeTypeOption(type)).toList()),
          ],
        ],
      ),
    );
  }

  // بناء خيار نوع الشحن
  Widget _buildRechargeTypeOption(String type) {
    final isSelected = _selectedRechargeType == type;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedRechargeType = type;
          _isRechargeTypeSelectorExpanded = false;

          // تحديث قيم الشحن حسب النوع المختار
          _updateRechargeAmountsForType(type);
        });
        _updateFormState();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? (Theme.of(context).brightness == Brightness.dark
                  ? AppTheme.primaryColor.withValues(alpha: 0.2)
                  : AppTheme.primaryColor.withValues(alpha: 0.1))
              : null,
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade700
                  : Colors.grey.shade200,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            // أيقونة نوع الشحن
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.primaryColor
                    : (Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade700
                        : Colors.grey.shade300),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.phone_android,
                color: isSelected
                    ? Colors.white
                    : (Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade400
                        : Colors.grey.shade600),
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            // اسم نوع الشحن
            Expanded(
              child: Text(
                type,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected
                      ? AppTheme.primaryColor
                      : (Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).textTheme.bodyLarge?.color
                          : Colors.black87),
                ),
              ),
            ),
            // أيقونة الاختيار
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppTheme.primaryColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  // بناء واجهة اختيار المحفظة الرقمية (نفس تصميم صفحة التوريد)
  Widget _buildWalletSelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade600
              : Colors.grey.shade300,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // الزر الرئيسي لاختيار المحفظة
          InkWell(
            onTap: () {
              setState(() {
                _isWalletSelectorExpanded = !_isWalletSelectorExpanded;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // أيقونة السهم
                  Icon(
                    _isWalletSelectorExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                  ),
                  const Spacer(),
                  // النص والأيقونة
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          _getSelectedWalletDisplayName(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: _selectedWallet == null
                                ? (Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey.shade400
                                    : Colors.grey.shade600)
                                : (Theme.of(context).brightness == Brightness.dark
                                    ? Theme.of(context).textTheme.bodyLarge?.color
                                    : Colors.black87),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.account_balance_wallet,
                          color: _selectedWallet == null
                              ? (Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey.shade600
                                  : Colors.grey.shade400)
                              : AppTheme.primaryColor,
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // خيارات المحافظ (قابلة للتوسيع) - تصميم Grid مثل صفحة التوريد
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isWalletSelectorExpanded ? null : 0,
            child: _isWalletSelectorExpanded
                ? Container(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      children: [
                        // المحافظ العادية في Grid
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 3,
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                          ),
                          itemCount: _wallets.where((w) => w['name'] != 'Instapay').length,
                          itemBuilder: (context, index) {
                            final walletsWithoutInstapay = _wallets.where((w) => w['name'] != 'Instapay').toList();
                            final wallet = walletsWithoutInstapay[index];
                            final isSelected = _selectedWallet == wallet['name'];

                            return InkWell(
                              onTap: () {
                                setState(() {
                                  _selectedWallet = wallet['name'];
                                  _isWalletSelectorExpanded = false;
                                });
                                _updateFormState();
                              },
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: isSelected
                                    ? AppTheme.primaryColor
                                    : (Theme.of(context).brightness == Brightness.dark
                                        ? Colors.grey.shade600
                                        : Colors.grey.shade300),
                                width: isSelected ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                              color: isSelected
                                  ? AppTheme.primaryColor.withValues(alpha: 0.1)
                                  : (Theme.of(context).brightness == Brightness.dark
                                      ? Theme.of(context).cardColor
                                      : Colors.white),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  wallet['icon'],
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? (wallet['color'] == Colors.red
                                          ? Colors.red.shade300
                                          : wallet['color'] == Colors.orange
                                              ? Colors.orange.shade300
                                              : wallet['color'] == Colors.green
                                                  ? Colors.green.shade300
                                                  : wallet['color'] == Colors.blue
                                                      ? Colors.blue.shade300
                                                      : wallet['color'])
                                      : wallet['color'],
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    wallet['displayName'],
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                      color: isSelected
                                          ? AppTheme.primaryColor
                                          : (Theme.of(context).brightness == Brightness.dark
                                              ? Theme.of(context).textTheme.bodyLarge?.color
                                              : Colors.black87),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),

                    // زر إنستاباي بعرض كامل (مثل صفحة التوريد)
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () {
                        setState(() {
                          _selectedWallet = 'Instapay';
                          _isWalletSelectorExpanded = false;
                        });
                        _updateFormState();
                      },
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: _selectedWallet == 'Instapay'
                                ? AppTheme.primaryColor
                                : (Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey.shade600
                                    : Colors.grey.shade300),
                            width: _selectedWallet == 'Instapay' ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                          color: _selectedWallet == 'Instapay'
                              ? AppTheme.primaryColor.withOpacity(0.1)
                              : (Theme.of(context).brightness == Brightness.dark
                                  ? Theme.of(context).cardColor
                                  : Colors.white),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.payment,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.blue.shade300
                                  : Colors.blue.shade500,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'إنستاباي',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: _selectedWallet == 'Instapay'
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: _selectedWallet == 'Instapay'
                                    ? AppTheme.primaryColor
                                    : (Theme.of(context).brightness == Brightness.dark
                                        ? Theme.of(context).textTheme.bodyLarge?.color
                                        : Colors.black87),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ))
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }



  // بناء مربعات اختيار المبلغ (ديناميكي، كل صف 4 مربعات)
  Widget _buildAmountGrid() {
    List<Widget> rows = [];

    // تقسيم القيم إلى صفوف، كل صف يحتوي على 4 مربعات
    for (int rowIndex = 0; rowIndex < (_rechargeAmounts.length / 4).ceil(); rowIndex++) {
      List<Widget> rowChildren = [];

      for (int colIndex = 0; colIndex < 4; colIndex++) {
        int index = rowIndex * 4 + colIndex;

        if (index < _rechargeAmounts.length) {
          // إضافة مربع القيمة
          rowChildren.add(
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  right: colIndex < 3 ? 8 : 0,
                ),
                child: _buildAmountBox(_rechargeAmounts[index]),
              ),
            ),
          );
        } else {
          // إضافة مساحة فارغة للحفاظ على التخطيط
          rowChildren.add(
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  right: colIndex < 3 ? 8 : 0,
                ),
                child: const SizedBox(height: 50),
              ),
            ),
          );
        }
      }

      rows.add(
        Row(children: rowChildren),
      );

      // إضافة مسافة بين الصفوف (إلا الصف الأخير)
      if (rowIndex < (_rechargeAmounts.length / 4).ceil() - 1) {
        rows.add(const SizedBox(height: 12));
      }
    }

    return Column(children: rows);
  }

  // بناء مربع مبلغ واحد
  Widget _buildAmountBox(double amount) {
    final isSelected = _selectedAmount == amount;

    return InkWell(
      onTap: () => _selectAmount(amount),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: 50,
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withValues(alpha: 0.1)
              : (Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).cardColor
                  : Colors.white),
          border: Border.all(
            color: isSelected
                ? AppTheme.primaryColor
                : (Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade600
                    : Colors.grey.shade300),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${amount == amount.roundToDouble() ? amount.toInt() : amount} ج',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: isSelected
                      ? AppTheme.primaryColor
                      : Theme.of(context).textTheme.bodyLarge?.color,
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  // التحقق من صحة رقم الهاتف المصري
  bool _isValidEgyptianPhoneNumber(String phone) {
    // إزالة المسافات والرموز
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // التحقق من الطول (11 رقم)
    if (cleanPhone.length != 11) return false;

    // التحقق من أن الرقم يبدأ بالأرقام الصحيحة
    final validPrefixes = ['010', '011', '012', '015'];
    final prefix = cleanPhone.substring(0, 3);

    return validPrefixes.contains(prefix);
  }

  // تحديث حالة النموذج
  void _updateFormState() {
    setState(() {
      // تحديث الحالة لإعادة بناء الواجهة وإعادة حساب المبلغ الإجمالي
      _updateTotalAmount();
    });
  }

  bool get _isFormValid {
    // التحقق من صحة رقم الهاتف المراد شحنه
    final phoneNumber = _phoneController.text.trim();
    final isPhoneValid = phoneNumber.isNotEmpty && _isValidEgyptianPhoneNumber(phoneNumber);

    final basicFieldsValid = isPhoneValid &&
                            _selectedRechargeType != null &&
                            _selectedAmount != null &&
                            _selectedWallet != null &&
                            _proofImageBase64 != null;

    // إذا تم اختيار إنستاباي، تحقق من اسم صاحب الحساب
    if (_selectedWallet == 'Instapay') {
      return basicFieldsValid && _instapayNameController.text.trim().isNotEmpty;
    }

    // للمحافظ الأخرى، تحقق من رقم المحفظة المرسلة
    final senderWallet = _senderWalletController.text.trim();
    final isSenderWalletValid = senderWallet.isNotEmpty && _isValidEgyptianPhoneNumber(senderWallet);

    return basicFieldsValid && isSenderWalletValid;
  }

  // بناء بطاقة معلومات التحويل (نفس تصميم صفحة التوريد)
  Widget _buildTransferInfoCard() {
    // معلومات إنستاباي أو المحافظ الأخرى
    final isInstapay = _selectedWallet == 'Instapay';
    final displayInfo = isInstapay ? 'wardlytec@instapay' : (_walletNumbers[_selectedWallet] ?? '');
    final titleText = isInstapay ? 'التحويل عبر إنستاباي' : 'رقم التحويل إلى $_selectedWallet';

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isInstapay
            ? (Theme.of(context).brightness == Brightness.dark
                ? Colors.blue.shade800.withOpacity(0.4)
                : Colors.blue.shade50)
            : (Theme.of(context).brightness == Brightness.dark
                ? Colors.green.shade900.withOpacity(0.3)
                : Colors.green.shade50),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isInstapay
              ? (Theme.of(context).brightness == Brightness.dark
                  ? Colors.blue.shade600
                  : Colors.blue.shade300)
              : (Theme.of(context).brightness == Brightness.dark
                  ? Colors.green.shade700
                  : Colors.green.shade300),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: isInstapay
                ? (Theme.of(context).brightness == Brightness.dark
                    ? Colors.blue.shade900.withValues(alpha: 0.2)
                    : Colors.blue.shade100)
                : (Theme.of(context).brightness == Brightness.dark
                    ? Colors.green.shade900.withValues(alpha: 0.2)
                    : Colors.green.shade100),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isInstapay ? Icons.payment : Icons.account_balance_wallet,
                color: isInstapay
                    ? (Theme.of(context).brightness == Brightness.dark
                        ? Colors.blue.shade200
                        : Colors.blue.shade700)
                    : (Theme.of(context).brightness == Brightness.dark
                        ? Colors.green.shade300
                        : Colors.green.shade700),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                titleText,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: isInstapay
                      ? (Theme.of(context).brightness == Brightness.dark
                          ? Colors.blue.shade200
                          : Colors.blue.shade700)
                      : (Theme.of(context).brightness == Brightness.dark
                          ? Colors.green.shade300
                          : Colors.green.shade700),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).cardColor
                  : Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isInstapay
                    ? (Theme.of(context).brightness == Brightness.dark
                        ? Colors.blue.shade500
                        : Colors.blue.shade200)
                    : (Theme.of(context).brightness == Brightness.dark
                        ? Colors.green.shade600
                        : Colors.green.shade200),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    displayInfo,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      letterSpacing: isInstapay ? 0.5 : 1.5,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).textTheme.bodyLarge?.color
                          : Colors.black87,
                    ),
                    textDirection: isInstapay ? TextDirection.rtl : TextDirection.ltr,
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    color: isInstapay
                        ? (Theme.of(context).brightness == Brightness.dark
                            ? Colors.blue.shade600
                            : Colors.blue.shade600)
                        : (Theme.of(context).brightness == Brightness.dark
                            ? Colors.green.shade700
                            : Colors.green.shade600),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.copy, color: Colors.white),
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: displayInfo));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Row(
                            children: [
                              const Icon(Icons.check_circle, color: Colors.white),
                              const SizedBox(width: 8),
                              Text(isInstapay ? 'تم نسخ اسم المستخدم بنجاح' : 'تم نسخ رقم المحفظة بنجاح'),
                            ],
                          ),
                          backgroundColor: isInstapay ? Colors.blue : Colors.green,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      );
                    },
                    tooltip: isInstapay ? 'نسخ اسم المستخدم' : 'نسخ الرقم',
                  ),
                ),
              ],
            ),
          ),

          // زر فتح إنستاباي (يظهر فقط عند اختيار إنستاباي)
          if (isInstapay) ...[
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: SingleTapButton(
                onPressed: _openInstapay,
                checkConnection: true, // فحص الاتصال قبل فتح Instapay
                connectionTitle: 'يتطلب اتصال بالإنترنت',
                connectionMessage: 'فتح تطبيق Instapay يتطلب اتصال بالإنترنت للوصول للرابط.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                icon: const Icon(Icons.open_in_new, color: Colors.white),
                loadingText: 'جاري فتح Instapay...',
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1E88E5),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'افتح إنستاباي',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],

          // النص التوضيحي
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: isInstapay
                    ? (Theme.of(context).brightness == Brightness.dark
                        ? Colors.blue.shade200
                        : Colors.blue.shade600)
                    : (Theme.of(context).brightness == Brightness.dark
                        ? Colors.green.shade300
                        : Colors.green.shade600),
                size: 16,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  isInstapay
                      ? 'اضغط على أيقونة النسخ لنسخ عنوان إنستاباي'
                      : 'اضغط على أيقونة النسخ لنسخ الرقم',
                  style: TextStyle(
                    fontSize: 12,
                    color: isInstapay
                        ? (Theme.of(context).brightness == Brightness.dark
                            ? Colors.blue.shade200
                            : Colors.blue.shade600)
                        : (Theme.of(context).brightness == Brightness.dark
                            ? Colors.green.shade300
                            : Colors.green.shade600),
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء ملخص المبلغ والعمولة (نفس تصميم صفحة التوريد)
  Widget _buildAmountSummary() {
    double effectiveCommission = _hasWatchedAd ? 0 : _commission;

    return Column(
      children: [
        // قسم معلومات العمولة (نفس تصميم صفحة التوريد)
        Container(
          width: double.infinity, // ← إضافة عرض ثابت للبطاقة
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.blue.shade900.withValues(alpha: 0.3)
                : Colors.blue.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.blue.shade700
                  : Colors.blue.shade200,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '💰 نسبة العمولة:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '• جنيه واحد ثابت على كل عملية شحن',
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
              Text(
                '• عمولة ثابتة: 1 جنيه فقط',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.green.shade300
                      : Colors.green,
                ),
              ),
              Consumer<SupabaseAuthProvider>(
                builder: (context, authProvider, child) {
                  final remainingViews = authProvider.currentUser?.rechargeAdViewsRemaining ?? 0;
                  return Text(
                    '• مشاهدة الإعلان: $remainingViews مرة متبقية x شهر',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: remainingViews <= 0
                          ? (Theme.of(context).brightness == Brightness.dark
                              ? Colors.red.shade300
                              : Colors.red)
                          : (Theme.of(context).brightness == Brightness.dark
                              ? Colors.blue.shade300
                              : Colors.blue),
                    ),
                  );
                },
              ),

              // زر مشاهدة الإعلان (نفس تصميم صفحة التوريد)
              Consumer<SupabaseAuthProvider>(
                builder: (context, authProvider, child) {
                  final remainingViews = authProvider.currentUser?.rechargeAdViewsRemaining ?? 0;
                  // إظهار الزر دائماً من البداية
                  return Column(
                    children: [
                      const SizedBox(height: 8),
                      const Divider(),
                      const SizedBox(height: 8),
                      SizedBox(
                        width: double.infinity,
                        child: SingleTapButton(
                          key: _adButtonKey,
                          onPressed: (_commission > 0 && remainingViews > 0 && !_hasWatchedAd && !_hasWatchedAdInSession) ? _watchAd : null,
                          checkConnection: true,
                          connectionTitle: 'يتطلب اتصال بالإنترنت',
                          connectionMessage: 'مشاهدة الإعلان تتطلب اتصال بالإنترنت لتحميل المحتوى.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                          icon: const Icon(Icons.play_circle_fill, color: Colors.white),
                          loadingText: 'جاري تحميل الإعلان...',
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            '💸 شاهد إعلان لإلغاء العمولة',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),

              // تم إزالة قسم "لا توجد إعلانات" لأن الزر سيكون ظاهراً دائماً

              // رسالة النجاح بعد مشاهدة الإعلان
              if (_hasWatchedAd) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.green.shade900.withValues(alpha: 0.3)
                        : Colors.green.shade100,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.green.shade300
                            : Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '🎉 ممتاز! ستحصل على الشحن بدون اضافه عمولة',
                          style: TextStyle(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.green.shade300
                                : Colors.green,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 16),

        // قسم ملخص المبلغ
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).cardColor
                : Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[600]!
                  : Colors.grey[300]!,
            ),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('قيمة الشحن:'),
                  Text(
                    '${_selectedAmount == _selectedAmount!.roundToDouble() ? _selectedAmount!.toInt() : _selectedAmount} جنيه',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('العمولة (1 ج):'),
                  Text(
                    '${effectiveCommission == effectiveCommission.roundToDouble() ? effectiveCommission.toInt() : effectiveCommission} جنيه',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: effectiveCommission > 0
                          ? (Theme.of(context).brightness == Brightness.dark
                              ? Colors.red.shade300
                              : Colors.red)
                          : (Theme.of(context).brightness == Brightness.dark
                              ? Colors.green.shade300
                              : Colors.green),
                    ),
                  ),
                ],
              ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('المبلغ المطلوب:', style: TextStyle(fontWeight: FontWeight.bold)),
                  Text(
                    '${_totalAmount == _totalAmount.roundToDouble() ? _totalAmount.toInt() : _totalAmount} جنيه',
                    style: const TextStyle(fontWeight: FontWeight.bold, color: AppTheme.primaryColor, fontSize: 16),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }



  // مشاهدة الإعلان (نفس منطق صفحة التوريد)
  Future<void> _watchAd() async {
    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);

    // التحقق من عدد مرات الاستخدام
    if (authProvider.currentUser == null || authProvider.currentUser!.rechargeAdViewsRemaining <= 0) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.warning, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    '⚠️ لقد استنفدت عدد مرات مشاهدة الإعلان المسموحة لهذا الشهر',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.orange[600],
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
      return;
    }

    // عرض حوار تحميل الإعلان
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return PopScope(
            canPop: false,
            child: const AlertDialog(
              title: Text(
                'تحضير الإعلان',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'جاري تحضير الإعلان...',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }

    // بدء مراقبة الاتصال أثناء تحميل الإعلان
    AdConnectionMonitor.startMonitoring(
      onConnectionLost: () {
        if (mounted) {
          // إغلاق حوار التحميل
          Navigator.of(context).pop();

          // إعادة تعيين حالة الإعلان للمحاولة مرة أخرى
          _resetAdStateForRetry();

          // إظهار رسالة
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.wifi_off, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'انقطع الاتصال أثناء تحضير الإعلان. يرجى المحاولة مرة أخرى',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.orange[600],
              duration: const Duration(seconds: 4),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      },
    );

    try {
      // التحقق من الاتصال مرة أخرى قبل عرض الإعلان
      final connectivityService = ConnectivityService();
      final isConnected = await connectivityService.checkConnection();

      if (!isConnected) {
        // إيقاف مراقبة الاتصال
        AdConnectionMonitor.stopMonitoring();

        // إغلاق حوار التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        print('❌ انقطع الاتصال أثناء تحضير الإعلان');
        if (mounted) {
          // إعادة تعيين حالة الإعلان للمحاولة مرة أخرى
          _resetAdStateForRetry();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.wifi_off, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'انقطع الاتصال أثناء تحضير الإعلان. يرجى المحاولة مرة أخرى',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.orange[600],
              duration: const Duration(seconds: 4),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
        return;
      }

      // محاولة عرض الإعلان
      final result = await AdManager.showRewardAd(context);

      // إيقاف مراقبة الاتصال
      AdConnectionMonitor.stopMonitoring();

      // إغلاق حوار التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      print('📋 نتيجة مشاهدة الإعلان: ${result ? "نجح" : "فشل"}');

      if (result) {
        print('✅ تم مشاهدة الإعلان بنجاح');
        // تم مشاهدة الإعلان بنجاح - الآن نحفظ الحالة
        await _saveAdWatchedState();

        final success = await authProvider.decrementRechargeAdViews();
        if (success && mounted) {
          setState(() {
            _hasWatchedAd = true;
          });

          // إعادة حساب المبلغ الإجمالي
          _updateTotalAmount();

          final remainingUses = authProvider.currentUser?.rechargeAdViewsRemaining ?? 0;
          // إظهار إشعار نجاح مشاهدة الإعلان
          RewardsNotificationService.showAdWatchedSuccessNotification(
            context,
            isSupplyAd: false,
            remainingViews: remainingUses,
          );
        }
      } else {
        // لم يتم إكمال مشاهدة الإعلان - إعادة تعيين الحالة للسماح بالمحاولة مرة أخرى
        print('❌ لم يتم إكمال مشاهدة الإعلان');
        _resetAdStateForRetry();

        RewardsNotificationService.showAdWatchFailedNotification(
          context,
          'لم يتم إكمال مشاهدة الإعلان',
        );
      }
    } catch (e) {
      // إيقاف مراقبة الاتصال في حالة الخطأ
      AdConnectionMonitor.stopMonitoring();

      // إغلاق حوار التحميل في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();

        // إعادة تعيين حالة الإعلان للمحاولة مرة أخرى
        _resetAdStateForRetry();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'خطأ في عرض الإعلان: $e',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    }
  }

  // حفظ حالة مشاهدة الإعلان في الجلسة الحالية
  Future<void> _saveAdWatchedState() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionKey = 'ad_watched_${_getSessionId()}';
    await prefs.setBool(sessionKey, true);

    if (mounted) {
      setState(() {
        _hasWatchedAdInSession = true;
      });
    }
  }

  // إنشاء معرف فريد للجلسة
  String _getSessionId() {
    if (_sessionId == null) {
      _sessionId = 'session_${DateTime.now().millisecondsSinceEpoch}';
      _cleanupOldSessions(); // تنظيف الجلسات القديمة
    }
    return _sessionId!;
  }

  // إنشاء session ID جديد (لإعادة تعيين حالة الإعلان)
  void _generateNewSessionId() {
    _sessionId = 'session_${DateTime.now().millisecondsSinceEpoch}';
    // إعادة تحميل حالة الإعلان للجلسة الجديدة
    _loadAdWatchedState();
  }

  // إعادة تعيين حالة الإعلان (للاستخدام بعد إرسال المعاملة)
  Future<void> _resetAdState() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionKey = 'ad_watched_${_getSessionId()}';
    await prefs.remove(sessionKey);

    if (mounted) {
      setState(() {
        _hasWatchedAd = false;
        _hasWatchedAdInSession = false;
      });
    }
  }

  // إعادة تعيين حالة الإعلان للمحاولة مرة أخرى (عند فشل الإعلان)
  void _resetAdStateForRetry() {
    if (mounted) {
      setState(() {
        _hasWatchedAdInSession = false; // السماح بالمحاولة مرة أخرى
      });

      // إعادة تفعيل زر الإعلان
      SingleTapButton.reactivateButton(_adButtonKey);
    }
  }

  // استعادة الاتصال - إعادة تفعيل زر الإعلان
  void _onConnectionRestored() async {
    if (mounted) {
      // إجبار ConnectivityService على إعادة فحص الاتصال
      final connectivityService = ConnectivityService();
      await connectivityService.checkConnection(forceCheck: true);

      // إعادة تفعيل زر الإعلان
      SingleTapButton.reactivateButton(_adButtonKey);
    }
  }

  // بناء قسم رفع صورة إثبات الدفع (نفس تصميم صفحة التوريد)
  Widget _buildProofImageSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _proofImageBase64 == null
            ? (Theme.of(context).brightness == Brightness.dark
                ? Colors.red.shade900.withValues(alpha: 0.3)
                : Colors.red.shade50)
            : (Theme.of(context).brightness == Brightness.dark
                ? Colors.green.shade900.withValues(alpha: 0.3)
                : Colors.green.shade50),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _proofImageBase64 == null
              ? (Theme.of(context).brightness == Brightness.dark
                  ? Colors.red.shade700
                  : Colors.red.shade300)
              : (Theme.of(context).brightness == Brightness.dark
                  ? Colors.green.shade700
                  : Colors.green.shade300),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.camera_alt,
                color: _proofImageBase64 == null
                    ? (Theme.of(context).brightness == Brightness.dark
                        ? Colors.red.shade300
                        : Colors.red)
                    : (Theme.of(context).brightness == Brightness.dark
                        ? Colors.green.shade300
                        : Colors.green),
                size: 20
              ),
              const SizedBox(width: 8),
              const Text(
                'صورة إثبات التحويل',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: _proofImageBase64 == null ? Colors.red : Colors.green,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _proofImageBase64 == null ? 'مطلوب' : 'تم',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'يرجى رفع صورة واضحة لإثبات التحويل من محفظتك الرقمية',
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade400
                  : Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 12),

          if (_proofImage == null) ...[
            // زر اختيار الصورة
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _pickProofImage,
                icon: const Icon(Icons.add_a_photo, size: 20),
                label: const Text('إضافة صورة'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Theme.of(context).brightness == Brightness.dark
                      ? Colors.blue.shade300
                      : Colors.blue,
                  side: BorderSide(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.blue.shade600
                        : Colors.blue.shade300,
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ] else ...[
            // عرض الصورة المختارة (نفس تصميم صفحة التوريد)
            Container(
              width: double.infinity,
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.green.shade600
                      : Colors.green.shade300,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.file(
                  _proofImage!,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.green.shade300
                      : Colors.green,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'تم اختيار صورة إثبات التحويل',
                    style: TextStyle(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.green.shade300
                          : Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.red.shade900.withOpacity(0.3)
                        : Colors.red.shade50,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.red.shade700
                          : Colors.red.shade300,
                      width: 1,
                    ),
                  ),
                  child: IconButton(
                    onPressed: _removeProofImage,
                    icon: Icon(
                      Icons.delete,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.red.shade300
                          : Colors.red,
                      size: 18,
                    ),
                    tooltip: 'حذف الصورة',
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 36,
                      minHeight: 36,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // 📸 اختيار صورة إثبات التحويل (نفس منطق صفحة التوريد)
  Future<void> _pickProofImage() async {
    try {
      // فحص جودة الاتصال قبل رفع الصورة
      final canUpload = await NetworkHelper.canPerformAction(
        context,
        actionType: ActionType.imageUpload,
        showDialog: true,
      );

      if (!canUpload) {
        // المستخدم اختار عدم المتابعة أو الاتصال ضعيف جداً
        return;
      }

      // عرض خيارات اختيار الصورة أولاً
      final source = await _showImageSourceDialog();
      if (source == null) return;

      // طلب الأذونات حسب المصدر المختار
      bool hasPermission = false;

      if (source == ImageSource.camera) {
        final cameraPermission = await Permission.camera.request();
        hasPermission = cameraPermission.isGranted;

        if (!hasPermission) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'يجب السماح بالوصول للكاميرا',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.orange[600],
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.all(16),
              ),
            );
          }
          return;
        }
      } else {
        // طلب أذونات المعرض
        hasPermission = await _requestGalleryPermissions();

        if (!hasPermission) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'يجب السماح بالوصول للمعرض',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.orange[600],
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.all(16),
              ),
            );
          }
          return;
        }
      }

      // اختيار الصورة
      try {
        final XFile? pickedFile = await _imagePicker.pickImage(
          source: source,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 80,
        );

        if (pickedFile != null) {
          final File imageFile = File(pickedFile.path);
          final List<int> imageBytes = await imageFile.readAsBytes();
          final String base64Image = base64Encode(imageBytes);

          setState(() {
            _proofImage = imageFile;
            _proofImageBase64 = base64Image;
            // تحديث حالة النموذج لتفعيل زر التحويل
          });
          _updateFormState();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'تم اختيار صورة إثبات التحويل بنجاح',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.green[600],
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.all(16),
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ حدث خطأ في اختيار الصورة: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ حدث خطأ في اختيار الصورة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 📸 طلب أذونات المعرض
  Future<bool> _requestGalleryPermissions() async {
    try {
      // محاولة طلب إذن الصور أولاً (Android 13+)
      PermissionStatus photosPermission = await Permission.photos.request();

      if (photosPermission.isGranted) {
        return true;
      }

      // إذا لم ينجح، نحاول إذن التخزين (للإصدارات الأقدم)
      PermissionStatus storagePermission = await Permission.storage.request();

      return storagePermission.isGranted;
    } catch (e) {
      print('خطأ في طلب أذونات المعرض: $e');
      return false;
    }
  }

  // 📸 عرض خيارات اختيار الصورة (نفس تصميم صفحة التوريد)
  Future<ImageSource?> _showImageSourceDialog() async {
    return await showModalBottomSheet<ImageSource>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // مؤشر السحب
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[600]
                      : Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),

              // العنوان
              const Text(
                'إضافة صورة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),

              // الخيارات
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildImageSourceOption(
                    icon: Icons.camera_alt,
                    label: 'التقاط صورة',
                    onTap: () => Navigator.of(context).pop(ImageSource.camera),
                  ),
                  _buildImageSourceOption(
                    icon: Icons.photo_library,
                    label: 'من المعرض',
                    onTap: () => Navigator.of(context).pop(ImageSource.gallery),
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  // بناء خيار مصدر الصورة (نفس تصميم صفحة التوريد)
  Widget _buildImageSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.blue.shade900.withValues(alpha: 0.3)
              : Colors.blue.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.blue.shade700
                : Colors.blue.shade200,
          ),
        ),
        child: Column(
          children: [
            Icon(icon, size: 40, color: AppTheme.primaryColor),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyLarge?.color
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 🗑️ حذف صورة إثبات التحويل (نفس منطق صفحة التوريد)
  void _removeProofImage() {
    setState(() {
      _proofImage = null;
      _proofImageBase64 = null;
      // تحديث حالة النموذج لتعطيل زر التحويل
    });
    _updateFormState();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.delete_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Expanded(
              child: Text(
                'تم حذف صورة إثبات التحويل',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange[600],
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  // إرسال طلب الشحن
  Future<void> _submitRecharge() async {
    // 🛡️ منع الضغط المتكرر
    if (_isSubmitting) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('⏳ جاري معالجة الطلب، يرجى الانتظار...'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

      final user = authProvider.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // إنشاء معاملة الشحن
      await transactionProvider.createRechargeTransaction(
        userId: user.id.toString(),
        phoneNumber: _phoneController.text.trim(),
        rechargeType: _selectedRechargeType!,
        amount: _selectedAmount!,
        commission: _hasWatchedAd ? 0 : _commission,
        totalAmount: _totalAmount,
        network: widget.selectedNetwork,
        paymentMethod: _selectedWallet!,
        senderWallet: _selectedWallet == 'Instapay'
            ? _instapayNameController.text.trim()
            : _senderWalletController.text.trim(),
        proofImageBase64: _proofImageBase64!,
        hasWatchedAd: _hasWatchedAd,
        userName: user.name ?? 'غير محدد',
        userPhoneNumber: user.phoneNumber,
      );

      if (mounted) {
        // عرض رسالة نجاح محسنة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تم إرسال طلبك! سيتم مراجعته قريباً',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green[600],
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );

        // إنشاء session جديد للمعاملة التالية
        _generateNewSessionId();

        // الانتقال إلى صفحة سجل المعاملات مع تحديد تبويب الشحن
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const TransactionHistoryScreen(
              initialTabIndex: 1, // تبويب الشحن
            ),
          ),
        );
      }
    } catch (e) {
      print('❌ خطأ في إرسال معاملة الشحن: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'حدث خطأ في إرسال معاملة الشحن:\n${e.toString()}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 6),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      showNoInternetCard: true,
      showQualityIndicator: false, // إخفاء مؤشر جودة الاتصال
      requiredQuality: ActionType.imageUpload, // تتطلب جودة جيدة لرفع الصور
      onConnectionRestored: _onConnectionRestored, // إعادة تفعيل زر الإعلان عند استعادة الاتصال
      child: Scaffold(
        appBar: AppBar(
          title: const Text('شحن رصيد جديد'),
          centerTitle: true,
        ),
        body: SafeArea(
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // أيقونة وعنوان الصفحة
                  Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(40),
                      ),
                      child: const Icon(Icons.phone_android, size: 40, color: AppTheme.primaryColor),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'شحن رصيد ${widget.selectedNetwork}',
                    style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'أدخل بيانات الشحن لإتمام العملية',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).textTheme.bodyMedium?.color
                          : Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),

                  // حقل رقم الهاتف
                  TextFormField(
                    controller: _phoneController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(11),
                    ],
                    decoration: InputDecoration(
                      labelText: 'رقم الهاتف المراد شحنه',
                      hintText: '***********',
                      prefixIcon: const Icon(Icons.phone),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.red, width: 2),
                      ),
                      helperText: _phoneController.text.trim().isNotEmpty && !_isValidEgyptianPhoneNumber(_phoneController.text.trim())
                          ? 'رقم الهاتف يجب أن يكون 11 رقم ويبدأ بـ 010, 011, 012, أو 015'
                          : null,
                      helperStyle: const TextStyle(color: Colors.red, fontSize: 12),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'يرجى إدخال رقم الهاتف';
                      if (!_isValidEgyptianPhoneNumber(value)) return 'رقم هاتف غير صحيح';
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // قائمة منسدلة لنوع الشحن (تصميم جديد)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نوع الشحن',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Theme.of(context).textTheme.bodyLarge?.color
                              : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildRechargeTypeSelector(),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // مربعات اختيار المبلغ
                  if (_selectedRechargeType != null) ...[
                    Text(
                      'اختر قيمة الشحن',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.bodyLarge?.color
                            : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildAmountGrid(),
                    const SizedBox(height: 16),
                  ],

                  // اختيار المحفظة الرقمية
                  if (_selectedAmount != null) ...[
                    Text(
                      'اختر المحفظة الإلكترونية',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.bodyLarge?.color
                            : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildWalletSelector(),

                    // بطاقة معلومات التحويل وحقل رقم المحفظة (بعد اختيار المحفظة)
                    if (_selectedWallet != null && (_walletNumbers[_selectedWallet] != null || _selectedWallet == 'Instapay')) ...[
                      const SizedBox(height: 16),
                      _buildTransferInfoCard(),
                    ],

                    // حقل رقم المحفظة المرسلة أو اسم صاحب حساب إنستاباي
                    if (_selectedWallet != null) ...[
                      const SizedBox(height: 16),
                      if (_selectedWallet != 'Instapay') ...[
                        // حقل رقم المحفظة للمحافظ العادية
                        TextFormField(
                          controller: _senderWalletController,
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(11),
                          ],
                          decoration: InputDecoration(
                            labelText: 'رقم محفظتك المرسلة منها',
                            hintText: '***********',
                            prefixIcon: const Icon(Icons.account_balance_wallet),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                            ),
                            errorBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(color: Colors.red, width: 2),
                            ),
                            helperText: _senderWalletController.text.trim().isNotEmpty && !_isValidEgyptianPhoneNumber(_senderWalletController.text.trim())
                                ? 'رقم الهاتف يجب أن يكون 11 رقم ويبدأ بـ 010, 011, 012, أو 015'
                                : null,
                            helperStyle: const TextStyle(color: Colors.red, fontSize: 12),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) return 'يرجى إدخال رقم محفظتك';
                            if (!_isValidEgyptianPhoneNumber(value)) return 'رقم هاتف غير صحيح';
                            return null;
                          },
                        ),
                      ] else ...[
                        // حقل اسم صاحب حساب إنستاباي
                        TextFormField(
                          controller: _instapayNameController,
                          keyboardType: TextInputType.name,
                          textCapitalization: TextCapitalization.words,
                          decoration: InputDecoration(
                            labelText: 'اسم صاحب حساب إنستاباي',
                            hintText: 'أدخل اسمك كما هو مسجل في إنستاباي',
                            prefixIcon: const Icon(Icons.person),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                            ),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال اسم صاحب الحساب';
                            }
                            if (value.trim().length < 2) {
                              return 'الاسم يجب أن يكون حرفين على الأقل';
                            }
                            return null;
                          },
                        ),
                      ],
                    ],

                    const SizedBox(height: 16), // مسافة بين المحافظ وبطاقة العمولة
                  ],



                  // عرض تفاصيل المبلغ والعمولة
                  if (_selectedAmount != null) ...[
                    _buildAmountSummary(),
                    const SizedBox(height: 16),
                  ],

                  // رفع صورة إثبات الدفع
                  if (_selectedWallet != null) ...[
                    _buildProofImageSection(),
                    const SizedBox(height: 24),
                  ],

                  // زر تم التحويل (يظهر عند اختيار قيمة الشحن)
                  if (_selectedAmount != null) ...[
                    Consumer<TransactionProvider>(
                      builder: (context, transactionProvider, child) {
                        return SingleTapButton(
                          onPressed: _isFormValid && !transactionProvider.isLoading && !_isSubmitting ? _submitRecharge : null,
                          checkConnection: true, // فحص الاتصال قبل إرسال المعاملة
                          connectionTitle: 'يتطلب اتصال بالإنترنت',
                          connectionMessage: 'إرسال طلب الشحن يتطلب اتصال بالإنترنت لمعالجة الطلب.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                          icon: (transactionProvider.isLoading || _isSubmitting)
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Icon(Icons.check_circle, size: 20),
                          loadingText: 'جاري المعالجة...',
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            minimumSize: const Size(double.infinity, 50),
                          ),
                          child: const Text(
                            'تم التحويل',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                        );
                      },
                    ),
                  ],

                  // بطاقة تنبيه مهم (أسفل زر تم التحويل) - تظهر عند اختيار قيمة الشحن
                  if (_selectedAmount != null) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.orange.shade900.withValues(alpha: 0.3)
                            : Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.orange.shade700
                              : Colors.orange.shade200,
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.warning_amber,
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.orange.shade300
                                    : Colors.orange,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'تنبيه مهم:',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.orange.shade300
                                      : Colors.orange,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'سيتم اضافه عمولة 1 جنيه على قيمة الشحن. شاهد الإعلان لتحصل على الشحن بدون عموله!',
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Theme.of(context).textTheme.bodyMedium?.color
                                  : Colors.grey,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
